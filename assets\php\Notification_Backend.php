<?php
/**
 * Bildirim Sistemi Backend Sınıfı
 * Bu dosya, bildirim sistemi için backend işlemlerini yönetir.
 *
 * @package DmrLMS
 * @since 1.0.6
 */

// <PERSON><PERSON><PERSON><PERSON> er<PERSON>im<PERSON> engelle
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Notification_Backend sınıfı
 */
class DMR_LMS_Notification_Backend {

    /**
     * Constructor
     */
    public function __construct() {
        // AJAX endpoint'lerini kaydet
        add_action('wp_ajax_dmr_get_notifications', array($this, 'get_notifications'));
        add_action('wp_ajax_dmr_mark_notification_read', array($this, 'mark_notification_read'));
        add_action('wp_ajax_dmr_mark_all_notifications_read', array($this, 'mark_all_notifications_read'));
        add_action('wp_ajax_dmr_delete_notification', array($this, 'delete_notification'));
        add_action('wp_ajax_dmr_get_notification_count', array($this, 'get_notification_count'));

        // Database tablosunu oluştur
        add_action('init', array($this, 'create_notifications_table'));
    }

    /**
     * Bildirim tablosunu oluştur
     */
    public function create_notifications_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'dmr_lms_notifications';
        
        // Tablo zaten varsa çık
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name) {
            return;
        }

        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE $table_name (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            type varchar(50) NOT NULL DEFAULT 'general',
            title varchar(255) NOT NULL,
            message text NOT NULL,
            status enum('read','unread') NOT NULL DEFAULT 'unread',
            user_id bigint(20) unsigned NOT NULL,
            post_id bigint(20) unsigned NULL,
            action_url varchar(500) NULL,
            icon varchar(50) NOT NULL DEFAULT 'bell',
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            read_at datetime NULL,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY status (status),
            KEY created_at (created_at)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Bildirim ekle
     *
     * @param array $data Bildirim verisi
     * @return int|false Bildirim ID veya false
     */
    public static function add_notification($data) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'dmr_lms_notifications';
        
        $defaults = array(
            'type' => 'general',
            'title' => '',
            'message' => '',
            'status' => 'unread',
            'user_id' => 0,
            'post_id' => null,
            'action_url' => null,
            'icon' => 'bell',
            'created_at' => current_time('mysql')
        );
        
        $data = wp_parse_args($data, $defaults);
        
        // Gerekli alanları kontrol et
        if (empty($data['title']) || empty($data['message']) || empty($data['user_id'])) {
            return false;
        }
        
        $result = $wpdb->insert($table_name, $data);
        
        if ($result === false) {
            return false;
        }
        
        return $wpdb->insert_id;
    }

    /**
     * Bildirimleri getir (AJAX)
     */
    public function get_notifications() {
        // Nonce kontrolü
        if (!wp_verify_nonce($_POST['nonce'], 'dmr_notification_nonce')) {
            wp_send_json_error(array('message' => 'Güvenlik kontrolü başarısız'));
            return;
        }

        // Kullanıcı kontrolü
        if (!is_user_logged_in()) {
            wp_send_json_error(array('message' => 'Giriş yapmanız gerekiyor'));
            return;
        }

        $user_id = get_current_user_id();
        $page = isset($_POST['page']) ? intval($_POST['page']) : 1;
        $per_page = isset($_POST['per_page']) ? intval($_POST['per_page']) : 20;
        $offset = ($page - 1) * $per_page;

        global $wpdb;
        $table_name = $wpdb->prefix . 'dmr_lms_notifications';

        // Bildirimleri getir
        $notifications = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $table_name 
             WHERE user_id = %d 
             ORDER BY created_at DESC 
             LIMIT %d OFFSET %d",
            $user_id, $per_page, $offset
        ));

        // Toplam bildirim sayısı
        $total_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name WHERE user_id = %d",
            $user_id
        ));

        // Okunmamış bildirim sayısı
        $unread_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name WHERE user_id = %d AND status = 'unread'",
            $user_id
        ));

        // Zaman formatını düzenle
        foreach ($notifications as &$notification) {
            $notification->time_ago = $this->time_ago($notification->created_at);
            $notification->formatted_date = date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($notification->created_at));
        }

        wp_send_json_success(array(
            'notifications' => $notifications,
            'total_count' => intval($total_count),
            'unread_count' => intval($unread_count),
            'current_page' => $page,
            'has_more' => ($offset + $per_page) < $total_count
        ));
    }

    /**
     * Bildirimi okundu işaretle (AJAX)
     */
    public function mark_notification_read() {
        // Nonce kontrolü
        if (!wp_verify_nonce($_POST['nonce'], 'dmr_notification_nonce')) {
            wp_send_json_error(array('message' => 'Güvenlik kontrolü başarısız'));
            return;
        }

        // Kullanıcı kontrolü
        if (!is_user_logged_in()) {
            wp_send_json_error(array('message' => 'Giriş yapmanız gerekiyor'));
            return;
        }

        $notification_id = isset($_POST['notification_id']) ? intval($_POST['notification_id']) : 0;
        $user_id = get_current_user_id();

        if (!$notification_id) {
            wp_send_json_error(array('message' => 'Geçersiz bildirim ID'));
            return;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'dmr_lms_notifications';

        $result = $wpdb->update(
            $table_name,
            array(
                'status' => 'read',
                'read_at' => current_time('mysql')
            ),
            array(
                'id' => $notification_id,
                'user_id' => $user_id
            ),
            array('%s', '%s'),
            array('%d', '%d')
        );

        if ($result === false) {
            wp_send_json_error(array('message' => 'Bildirim güncellenemedi'));
            return;
        }

        // Güncel okunmamış sayısını getir
        $unread_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name WHERE user_id = %d AND status = 'unread'",
            $user_id
        ));

        wp_send_json_success(array(
            'message' => 'Bildirim okundu işaretlendi',
            'unread_count' => intval($unread_count)
        ));
    }

    /**
     * Tüm bildirimleri okundu işaretle (AJAX)
     */
    public function mark_all_notifications_read() {
        // Nonce kontrolü
        if (!wp_verify_nonce($_POST['nonce'], 'dmr_notification_nonce')) {
            wp_send_json_error(array('message' => 'Güvenlik kontrolü başarısız'));
            return;
        }

        // Kullanıcı kontrolü
        if (!is_user_logged_in()) {
            wp_send_json_error(array('message' => 'Giriş yapmanız gerekiyor'));
            return;
        }

        $user_id = get_current_user_id();

        global $wpdb;
        $table_name = $wpdb->prefix . 'dmr_lms_notifications';

        $result = $wpdb->update(
            $table_name,
            array(
                'status' => 'read',
                'read_at' => current_time('mysql')
            ),
            array(
                'user_id' => $user_id,
                'status' => 'unread'
            ),
            array('%s', '%s'),
            array('%d', '%s')
        );

        if ($result === false) {
            wp_send_json_error(array('message' => 'Bildirimler güncellenemedi'));
            return;
        }

        wp_send_json_success(array(
            'message' => 'Tüm bildirimler okundu işaretlendi',
            'unread_count' => 0,
            'updated_count' => intval($result)
        ));
    }

    /**
     * Bildirimi sil (AJAX)
     */
    public function delete_notification() {
        // Nonce kontrolü
        if (!wp_verify_nonce($_POST['nonce'], 'dmr_notification_nonce')) {
            wp_send_json_error(array('message' => 'Güvenlik kontrolü başarısız'));
            return;
        }

        // Kullanıcı kontrolü
        if (!is_user_logged_in()) {
            wp_send_json_error(array('message' => 'Giriş yapmanız gerekiyor'));
            return;
        }

        $notification_id = isset($_POST['notification_id']) ? intval($_POST['notification_id']) : 0;
        $user_id = get_current_user_id();

        if (!$notification_id) {
            wp_send_json_error(array('message' => 'Geçersiz bildirim ID'));
            return;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'dmr_lms_notifications';

        $result = $wpdb->delete(
            $table_name,
            array(
                'id' => $notification_id,
                'user_id' => $user_id
            ),
            array('%d', '%d')
        );

        if ($result === false) {
            wp_send_json_error(array('message' => 'Bildirim silinemedi'));
            return;
        }

        // Güncel sayıları getir
        $total_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name WHERE user_id = %d",
            $user_id
        ));

        $unread_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name WHERE user_id = %d AND status = 'unread'",
            $user_id
        ));

        wp_send_json_success(array(
            'message' => 'Bildirim silindi',
            'total_count' => intval($total_count),
            'unread_count' => intval($unread_count)
        ));
    }

    /**
     * Bildirim sayısını getir (AJAX)
     */
    public function get_notification_count() {
        // Kullanıcı kontrolü
        if (!is_user_logged_in()) {
            wp_send_json_error(array('message' => 'Giriş yapmanız gerekiyor'));
            return;
        }

        $user_id = get_current_user_id();

        global $wpdb;
        $table_name = $wpdb->prefix . 'dmr_lms_notifications';

        $unread_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name WHERE user_id = %d AND status = 'unread'",
            $user_id
        ));

        wp_send_json_success(array(
            'unread_count' => intval($unread_count)
        ));
    }

    /**
     * Zaman farkını hesapla
     *
     * @param string $datetime
     * @return string
     */
    private function time_ago($datetime) {
        $time = time() - strtotime($datetime);

        if ($time < 60) {
            return 'Az önce';
        } elseif ($time < 3600) {
            $minutes = floor($time / 60);
            return $minutes . ' dakika önce';
        } elseif ($time < 86400) {
            $hours = floor($time / 3600);
            return $hours . ' saat önce';
        } elseif ($time < 2592000) {
            $days = floor($time / 86400);
            return $days . ' gün önce';
        } else {
            return date_i18n(get_option('date_format'), strtotime($datetime));
        }
    }
}
