/**
 * Bildirim Sistemi JavaScript
 * Bu dosya, bildirim butonu ve sidebar fonksiyonalitesini yönetir.
 *
 * @package DmrLMS
 * @since 1.0.6
 */

(function() {
    'use strict';

    // DOM yüklendikten sonra çalıştır
    document.addEventListener('DOMContentLoaded', function() {
        initNotificationSystem();

        // Sayfa yüklendiğinde bildirim sayısını getir
        if (typeof dmrNotificationData !== 'undefined') {
            loadNotificationCount();
        }
    });

    /**
     * Bildirim sistemini başlat
     */
    function initNotificationSystem() {
        const notificationButton = document.getElementById('tutor-notification-button');
        const notificationOverlay = document.getElementById('tutor-notification-overlay');
        const notificationSidebar = document.getElementById('tutor-notification-sidebar');
        const notificationClose = document.getElementById('tutor-notification-close');

        // Elementlerin varlığını kontrol et
        if (!notificationButton || !notificationOverlay || !notificationSidebar || !notificationClose) {
            console.warn('Bildirim sistemi elementleri bulunamadı');
            return;
        }

        // Event listener'ları ekle
        setupEventListeners(notificationButton, notificationOverlay, notificationSidebar, notificationClose);

        // Klavye erişilebilirliği
        setupKeyboardAccessibility(notificationOverlay, notificationClose);

        console.log('Bildirim sistemi başarıyla başlatıldı');
    }

    /**
     * Event listener'ları ayarla
     */
    function setupEventListeners(button, overlay, sidebar, closeBtn) {
        // Bildirim butonuna tıklama
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            toggleNotificationSidebar(overlay, sidebar, true);
        });

        // Kapatma butonuna tıklama
        closeBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            toggleNotificationSidebar(overlay, sidebar, false);
        });

        // Overlay'e tıklama (sidebar dışına tıklama)
        overlay.addEventListener('click', function(e) {
            if (e.target === overlay) {
                toggleNotificationSidebar(overlay, sidebar, false);
            }
        });

        // Escape tuşu ile kapatma
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && overlay.classList.contains('active')) {
                toggleNotificationSidebar(overlay, sidebar, false);
            }
        });

        // Sayfa scroll'unu engelle (sidebar açıkken)
        overlay.addEventListener('transitionend', function() {
            if (overlay.classList.contains('active')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        });
    }

    /**
     * Klavye erişilebilirliğini ayarla
     */
    function setupKeyboardAccessibility(overlay, closeBtn) {
        // Tab tuşu ile focus yönetimi
        overlay.addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                trapFocus(e, overlay);
            }
        });

        // İlk focus'u kapatma butonuna ver
        overlay.addEventListener('transitionend', function() {
            if (overlay.classList.contains('active')) {
                closeBtn.focus();
            }
        });
    }

    /**
     * Focus'u sidebar içinde tut
     */
    function trapFocus(e, container) {
        const focusableElements = container.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (e.shiftKey) {
            if (document.activeElement === firstElement) {
                e.preventDefault();
                lastElement.focus();
            }
        } else {
            if (document.activeElement === lastElement) {
                e.preventDefault();
                firstElement.focus();
            }
        }
    }

    /**
     * Bildirim sidebar'ını aç/kapat
     */
    function toggleNotificationSidebar(overlay, sidebar, show) {
        const button = document.getElementById('tutor-notification-button');

        if (show) {
            // Sidebar'ı aç
            overlay.classList.add('active');

            // Animasyon için kısa gecikme
            setTimeout(() => {
                sidebar.classList.add('active');
            }, 10);

            // Buton aktif durumunu ekle
            if (button) {
                button.classList.add('active');
            }

            // Buton animasyonu
            animateNotificationButton(true);

            // Aria durumunu güncelle
            updateAriaStates(true);

        } else {
            // Sidebar'ı kapat
            sidebar.classList.remove('active');
            overlay.classList.remove('active');

            // Buton aktif durumunu kaldır
            if (button) {
                button.classList.remove('active');
            }

            // Buton animasyonu
            animateNotificationButton(false);

            // Aria durumunu güncelle
            updateAriaStates(false);

            // Body scroll'unu geri yükle
            document.body.style.overflow = '';
        }
    }

    /**
     * Bildirim butonuna animasyon ekle
     */
    function animateNotificationButton(isOpening) {
        const button = document.getElementById('tutor-notification-button');
        if (!button) return;

        if (isOpening) {
            // Açılma animasyonu
            button.style.transform = 'scale(0.9)';
            button.style.backgroundColor = 'rgba(0, 123, 255, 0.1)';
            
            setTimeout(() => {
                button.style.transform = 'scale(1)';
            }, 150);
        } else {
            // Kapanma animasyonu
            button.style.transform = 'scale(1.1)';
            button.style.backgroundColor = '';
            
            setTimeout(() => {
                button.style.transform = 'scale(1)';
            }, 150);
        }
    }

    /**
     * ARIA durumlarını güncelle
     */
    function updateAriaStates(isOpen) {
        const button = document.getElementById('tutor-notification-button');
        const overlay = document.getElementById('tutor-notification-overlay');

        if (button) {
            button.setAttribute('aria-expanded', isOpen.toString());
            button.setAttribute('aria-label', isOpen ? 'Bildirimleri Kapat' : 'Bildirimleri Aç');
        }

        if (overlay) {
            overlay.setAttribute('aria-hidden', (!isOpen).toString());
        }
    }

    /**
     * Bildirim sayısını güncelle (gelecekte kullanılacak)
     */
    function updateNotificationCount(count) {
        const badge = document.getElementById('tutor-notification-badge');
        if (!badge) return;

        if (count > 0) {
            badge.textContent = count > 99 ? '99+' : count.toString();
            badge.style.display = 'flex';
        } else {
            badge.style.display = 'none';
        }
    }

    /**
     * Bildirim listesini güncelle (gelecekte kullanılacak)
     */
    function updateNotificationList(notifications) {
        const list = document.getElementById('tutor-notification-list');
        const empty = document.getElementById('tutor-notification-empty');
        const markAllBtn = document.getElementById('tutor-notification-mark-all-read');

        if (!list || !empty || !markAllBtn) return;

        if (notifications && notifications.length > 0) {
            // Bildirimleri göster
            empty.style.display = 'none';
            list.style.display = 'block';
            markAllBtn.style.display = 'flex';

            // Bildirim HTML'ini oluştur (şimdilik boş)
            list.innerHTML = '';
        } else {
            // Boş durumu göster
            empty.style.display = 'flex';
            list.style.display = 'none';
            markAllBtn.style.display = 'none';
        }
    }

    /**
     * Bildirim sayısını backend'den yükle
     */
    function loadNotificationCount() {
        if (typeof dmrNotificationData === 'undefined') {
            return;
        }

        fetch(dmrNotificationData.ajax_url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'dmr_get_notification_count',
                nonce: dmrNotificationData.nonce
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateNotificationCount(data.data.unread_count);
            }
        })
        .catch(error => {
            console.warn('Bildirim sayısı yüklenemedi:', error);
        });
    }

    /**
     * Bildirimleri backend'den yükle
     */
    function loadNotifications(page = 1) {
        if (typeof dmrNotificationData === 'undefined') {
            return;
        }

        const list = document.getElementById('tutor-notification-list');
        const empty = document.getElementById('tutor-notification-empty');

        if (!list || !empty) return;

        // Loading göster
        if (page === 1) {
            list.innerHTML = '<div class="tutor-notification-loading">Yükleniyor...</div>';
            empty.style.display = 'none';
        }

        fetch(dmrNotificationData.ajax_url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'dmr_get_notifications',
                nonce: dmrNotificationData.nonce,
                page: page,
                per_page: 20
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const notifications = data.data.notifications;
                const unreadCount = data.data.unread_count;

                // Bildirim sayısını güncelle
                updateNotificationCount(unreadCount);

                // Bildirimleri göster
                if (notifications && notifications.length > 0) {
                    displayNotifications(notifications, page === 1);
                    empty.style.display = 'none';

                    // "Tümünü Okundu İşaretle" butonunu göster
                    const markAllBtn = document.getElementById('tutor-notification-mark-all-read');
                    if (markAllBtn && unreadCount > 0) {
                        markAllBtn.style.display = 'flex';
                    }
                } else if (page === 1) {
                    list.innerHTML = '';
                    empty.style.display = 'flex';
                }
            } else {
                console.error('Bildirimler yüklenemedi:', data.data.message);
                if (page === 1) {
                    list.innerHTML = '<div class="tutor-notification-error">Bildirimler yüklenemedi</div>';
                }
            }
        })
        .catch(error => {
            console.error('AJAX hatası:', error);
            if (page === 1) {
                list.innerHTML = '<div class="tutor-notification-error">Bağlantı hatası</div>';
            }
        });
    }

    /**
     * Bildirimleri HTML olarak göster
     */
    function displayNotifications(notifications, clearFirst = true) {
        const list = document.getElementById('tutor-notification-list');
        if (!list) return;

        if (clearFirst) {
            list.innerHTML = '';
        }

        notifications.forEach(notification => {
            const notificationHtml = createNotificationHTML(notification);
            list.insertAdjacentHTML('beforeend', notificationHtml);
        });

        // Event listener'ları ekle
        setupNotificationEvents();
    }

    /**
     * Tek bildirim HTML'i oluştur
     */
    function createNotificationHTML(notification) {
        const isUnread = notification.status === 'unread';
        const unreadClass = isUnread ? 'unread' : '';
        const iconClass = getNotificationIcon(notification.type);

        return `
            <div class="tutor-notification-item ${unreadClass}" data-id="${notification.id}">
                <div class="tutor-notification-item-icon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        ${getNotificationIconSVG(notification.type)}
                    </svg>
                </div>
                <div class="tutor-notification-item-content">
                    <h4 class="tutor-notification-item-title">${notification.title}</h4>
                    <p class="tutor-notification-item-message">${notification.message}</p>
                    <span class="tutor-notification-item-time">${notification.time_ago}</span>
                </div>
                <div class="tutor-notification-item-actions">
                    ${isUnread ? '<button class="tutor-notification-mark-read" title="Okundu İşaretle"><svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="20,6 9,17 4,12"></polyline></svg></button>' : ''}
                    <button class="tutor-notification-delete" title="Sil"><svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg></button>
                </div>
            </div>
        `;
    }

    /**
     * Bildirim türüne göre ikon getir
     */
    function getNotificationIcon(type) {
        const icons = {
            'course': 'book-open',
            'assignment': 'assignment',
            'quiz': 'quiz',
            'announcement': 'megaphone',
            'message': 'envelope',
            'system': 'cog',
            'general': 'bell'
        };

        return icons[type] || 'bell';
    }

    /**
     * Bildirim türüne göre SVG ikon getir
     */
    function getNotificationIconSVG(type) {
        const icons = {
            'course': '<path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path><path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>',
            'assignment': '<path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14,2 14,8 20,8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10,9 9,9 8,9"></polyline>',
            'quiz': '<circle cx="12" cy="12" r="10"></circle><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path><line x1="12" y1="17" x2="12.01" y2="17"></line>',
            'announcement': '<path d="M3 11l18-5v12L3 14v-3z"></path><path d="M11.6 16.8a3 3 0 1 1-5.8-1.6"></path>',
            'message': '<path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path><polyline points="22,6 12,13 2,6"></polyline>',
            'system': '<circle cx="12" cy="12" r="3"></circle><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>',
            'general': '<path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path><path d="M13.73 21a2 2 0 0 1-3.46 0"></path>'
        };

        return icons[type] || icons['general'];
    }

    /**
     * Bildirim event listener'larını ayarla
     */
    function setupNotificationEvents() {
        // Okundu işaretle butonları
        document.querySelectorAll('.tutor-notification-mark-read').forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation();
                const notificationItem = this.closest('.tutor-notification-item');
                const notificationId = notificationItem.dataset.id;
                markNotificationAsRead(notificationId, notificationItem);
            });
        });

        // Silme butonları
        document.querySelectorAll('.tutor-notification-delete').forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation();
                const notificationItem = this.closest('.tutor-notification-item');
                const notificationId = notificationItem.dataset.id;
                deleteNotification(notificationId, notificationItem);
            });
        });

        // Tümünü okundu işaretle butonu
        const markAllBtn = document.getElementById('tutor-notification-mark-all-read');
        if (markAllBtn) {
            markAllBtn.addEventListener('click', function() {
                markAllNotificationsAsRead();
            });
        }
    }

    /**
     * Bildirimi okundu işaretle
     */
    function markNotificationAsRead(notificationId, notificationElement) {
        if (typeof dmrNotificationData === 'undefined') {
            return;
        }

        fetch(dmrNotificationData.ajax_url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'dmr_mark_notification_read',
                nonce: dmrNotificationData.nonce,
                notification_id: notificationId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // UI'ı güncelle
                notificationElement.classList.remove('unread');
                const markButton = notificationElement.querySelector('.tutor-notification-mark-read');
                if (markButton) {
                    markButton.remove();
                }

                // Bildirim sayısını güncelle
                updateNotificationCount(data.data.unread_count);

                // Eğer hiç okunmamış bildirim kalmadıysa "Tümünü Okundu İşaretle" butonunu gizle
                if (data.data.unread_count === 0) {
                    const markAllBtn = document.getElementById('tutor-notification-mark-all-read');
                    if (markAllBtn) {
                        markAllBtn.style.display = 'none';
                    }
                }
            }
        })
        .catch(error => {
            console.error('Bildirim okundu işaretlenemedi:', error);
        });
    }

    /**
     * Bildirimi sil
     */
    function deleteNotification(notificationId, notificationElement) {
        if (typeof dmrNotificationData === 'undefined') {
            return;
        }

        if (!confirm('Bu bildirimi silmek istediğinizden emin misiniz?')) {
            return;
        }

        fetch(dmrNotificationData.ajax_url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'dmr_delete_notification',
                nonce: dmrNotificationData.nonce,
                notification_id: notificationId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // UI'dan kaldır
                notificationElement.remove();

                // Bildirim sayısını güncelle
                updateNotificationCount(data.data.unread_count);

                // Eğer hiç bildirim kalmadıysa boş durumu göster
                const list = document.getElementById('tutor-notification-list');
                const empty = document.getElementById('tutor-notification-empty');
                if (list && empty && data.data.total_count === 0) {
                    list.innerHTML = '';
                    empty.style.display = 'flex';

                    const markAllBtn = document.getElementById('tutor-notification-mark-all-read');
                    if (markAllBtn) {
                        markAllBtn.style.display = 'none';
                    }
                }
            }
        })
        .catch(error => {
            console.error('Bildirim silinemedi:', error);
        });
    }

    /**
     * Tüm bildirimleri okundu işaretle
     */
    function markAllNotificationsAsRead() {
        if (typeof dmrNotificationData === 'undefined') {
            return;
        }

        fetch(dmrNotificationData.ajax_url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'dmr_mark_all_notifications_read',
                nonce: dmrNotificationData.nonce
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Tüm bildirimleri okundu olarak işaretle
                document.querySelectorAll('.tutor-notification-item.unread').forEach(item => {
                    item.classList.remove('unread');
                    const markButton = item.querySelector('.tutor-notification-mark-read');
                    if (markButton) {
                        markButton.remove();
                    }
                });

                // Bildirim sayısını güncelle
                updateNotificationCount(0);

                // "Tümünü Okundu İşaretle" butonunu gizle
                const markAllBtn = document.getElementById('tutor-notification-mark-all-read');
                if (markAllBtn) {
                    markAllBtn.style.display = 'none';
                }
            }
        })
        .catch(error => {
            console.error('Bildirimler okundu işaretlenemedi:', error);
        });
    }

    /**
     * Sidebar açıldığında bildirimleri yükle
     */
    function loadNotificationsOnOpen() {
        loadNotifications(1);
    }

    /**
     * Global fonksiyonları window objesine ekle
     */
    window.TutorNotificationSystem = {
        updateCount: updateNotificationCount,
        updateList: updateNotificationList,
        loadNotifications: loadNotifications,
        loadNotificationCount: loadNotificationCount,
        toggle: function(show) {
            const overlay = document.getElementById('tutor-notification-overlay');
            const sidebar = document.getElementById('tutor-notification-sidebar');
            if (overlay && sidebar) {
                toggleNotificationSidebar(overlay, sidebar, show);

                // Sidebar açılırken bildirimleri yükle
                if (show) {
                    setTimeout(loadNotificationsOnOpen, 100);
                }
            }
        }
    };

})();
